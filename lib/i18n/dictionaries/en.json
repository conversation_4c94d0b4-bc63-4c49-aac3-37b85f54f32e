{"common": {"appName": "Paws & Whiskers", "tagline": "Find your perfect feline companion", "navigation": {"home": "Home", "cats": "Cats", "about": "About", "contact": "Contact", "login": "<PERSON><PERSON>", "register": "Register", "profile": "Profile", "dashboard": "Dashboard", "logout": "Logout"}, "error": "Error", "any": "Any", "loading": "Loading", "placeholders": {"user": "User", "cat": "Cat", "image": "Image"}, "yes": "Yes", "no": "No", "edit": "Edit", "user": "User", "anonymous": "Anonymous"}, "home": {"hero": {"title": "Find Your Perfect Companion", "subtitle": "Adopt a cat and change two lives forever - theirs and yours.", "description": "Connect with adorable cats looking for their forever homes. Every whisker, every purr, every gentle paw is waiting to bring joy to your life.", "cta": "Find a Cat"}, "search": {"placeholder": "Search for cats...", "button": "Search"}, "location": {"unknown": "Location not specified"}, "howItWorks": {"title": "How It Works", "subtitle": "Finding your perfect feline companion is simple and secure", "step1": {"title": "Find a Cat", "description": "Browse through hundreds of adorable cats"}, "step2": {"title": "Chat with Owner", "description": "Connect directly with shelters and caretakers"}, "step3": {"title": "Complete Adoption", "description": "Meet your new family member"}}, "mission": {"title": "Our Mission", "description": "We connect loving homes with cats in need. Our platform brings together rescuers, adopters, and veterinary clinics to ensure every cat finds their forever home.", "disclaimer": "Buying and selling cats is strictly prohibited - we support adoption only."}, "featured": {"title": "Featured Cats", "subtitle": "Meet some of our adorable cats looking for a home", "viewAll": "View All Cats"}, "stats": {"title": "Making a Difference", "adoptions": "Cats Adopted", "rescues": "Rescues", "volunteers": "Volunteers", "clinics": "Partner Clinics"}, "roles": {"title": "How You Can Help", "subtitle": "Join our community and make a difference in the lives of cats in need", "adopter": {"title": "For Adopters", "description": "Find your perfect feline companion and give them a loving forever home.", "cta": "Browse Cats"}, "rescuer": {"title": "For Rescuers", "description": "List cats for adoption and connect with potential adopters.", "cta": "Register as Rescuer"}, "clinic": {"title": "For Clinics", "description": "Create a profile for your veterinary clinic and help cats find homes.", "cta": "Register as Clinic"}}}, "cats": {"title": "Available Cats", "description": "Find your perfect feline companion from our selection of cats available for adoption", "viewAll": "View All Cats", "filters": {"title": "Filters", "age": "Age", "ageRange": "Age Range", "years": "years", "to": "to", "gender": "Gender", "male": "Male", "female": "Female", "breed": "Breed", "breeds": "breeds", "location": "Location", "wilaya": "<PERSON><PERSON><PERSON>", "wilayas": "wilayas", "commune": "<PERSON><PERSON><PERSON>", "communes": "communes", "specialNeeds": "Special Needs", "vaccinated": "Vaccinated", "neutered": "Neutered", "available": "Available", "apply": "Apply Filters", "clear": "Clear Filters", "resetAll": "Reset All", "moreFilters": "More Filters", "sortBy": "Sort By", "selectBreed": "Select a breed", "selectWilaya": "Select a wilaya", "selectCommune": "Select a commune", "selectWilayaFirst": "Select wilaya first"}, "sort": {"newest": "Newest", "oldest": "Oldest", "nameAZ": "Name A-Z", "nameZA": "Name Z-A"}, "card": {"age": "Age", "gender": "Gender", "location": "Location", "edit": "Edit", "viewDetails": "View Details", "addToFavorites": "Add to Favorites", "removeFromFavorites": "Remove from Favorites"}, "status": {"available": "Available", "pending": "Pending", "adopted": "Adopted", "fostered": "Fostered", "draft": "Draft", "draftMessage": "Draft - not visible to others", "approved": "Approved", "rejected": "Rejected", "unavailable": "Unavailable", "changeStatus": "Change Status", "updateSuccess": "Status Updated", "updateDescription": "Cat status changed to {status}", "updateError": "Failed to update status"}, "actions": {"publish": "Publish", "unpublish": "Unpublish"}, "errors": {"failedToLoad": "Failed to load cats", "noFeatured": "No featured cats available", "loadingError": "Error loading cats", "genericError": "An error occurred. Please try again."}, "loading": "Loading cats...", "retry": "Retry", "noCatsFound": "No cats found", "adjustFilters": "Try adjusting your filters to find more cats.", "search": {"placeholder": "Search by name, breed, location...", "clear": "Clear search", "resultsCount": "Found {count} cats for \"{query}\"", "noResultsFor": "No cats found for \"{query}\"", "tryDifferentTerms": "Try different search terms or adjust your filters.", "suggestions": {"tryDifferent": "Try different search terms:", "checkSpelling": "Check your spelling", "useGeneral": "Use more general terms", "tryBreed": "Try searching by breed name", "tryLocation": "Try searching by location"}}, "gallery": {"photoPlaceholder": "Cat Photo", "thumbnailPlaceholder": "Photo", "mainPhoto": "main photo", "photo": "photo", "fullSizePhoto": "full size photo"}, "notFound": "Cat Not Found", "notFoundDescription": "The requested cat could not be found.", "adoption": "Cat Adoption", "adoptNow": "<PERSON><PERSON><PERSON>", "today": "today", "share": "Share", "breedMixed": "Mixed", "viewProfile": "View Profile", "postedByShelter": "Posted by shelter staff", "location": "Location", "mapLocationFor": "Map location for", "details": {"about": "About", "story": "Story", "health": "Health", "noDescription": "No description available.", "noLocation": "Location not specified", "postedOn": "Posted on: {date}", "noStory": "No story available for this cat yet."}, "adoptionFee": "Adoption Fee", "startChatToAdopt": "Start Chat to Adopt", "askQuestion": "Ask a Question", "goodWith": "Good With", "kids": "Kids", "otherPets": "Other pets", "activeFamilies": "Active families", "currentCaretaker": "Current Caretaker", "size": "Size", "small": "Small", "medium": "Medium", "large": "Large", "breed": "Breed", "age": "Age", "gender": "Gender", "spayedNeutered": "Spayed/Neutered", "healthStatus": "Health Status", "backToAllCats": "Back to all cats", "upToDate": "up to date", "favorite": "Favorite", "edit": {"title": "Edit Cat", "description": "Edit cat listing"}, "new": {"title": "Add New Cat", "description": "List a new cat for adoption"}, "drafts": "Drafts"}, "profile": {"title": "Profile", "description": "Manage your profile, listed cats, and favorites", "overview": "Overview", "overviewDescription": "Your dashboard overview and quick stats", "notifications": "Notifications", "notificationsDescription": "Manage your notification preferences", "memberSince": "Member since", "settings": {"title": "Settings", "description": "Manage your account settings and preferences", "profileInfo": "Profile Information", "password": "Password", "updatePersonalInfo": "Update your personal information", "profilePicture": "Profile Picture", "profilePictureDescription": "Upload a photo to personalize your account", "name": "Name", "namePlaceholder": "Your name", "email": "Email", "emailDescription": "Your email address cannot be changed", "bio": "Bio", "bioPlaceholder": "Tell us a bit about yourself", "bioDescription": "This will be displayed on your public profile", "location": "Location", "locationPlaceholder": "City, State", "wilaya": "<PERSON><PERSON><PERSON>", "wilayaPlaceholder": "Select wilaya", "commune": "<PERSON><PERSON><PERSON>", "communePlaceholder": "Select commune", "selectWilayaFirst": "Select wilaya first", "loadingWilayas": "Loading wilayas...", "loadingCommunes": "Loading communes...", "phone": "Phone", "phonePlaceholder": "Your phone number", "saving": "Saving...", "saveChanges": "Save Changes", "changePassword": "Change Password", "changePasswordDescription": "Update your password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "passwordRequirement": "Password must be at least 8 characters long", "updating": "Updating...", "updateProfile": "Update Profile", "updatePassword": "Update Password", "profileUpdated": "Profile updated", "profileUpdatedDescription": "Your profile information has been updated.", "profileUpdateError": "Your profile couldn't be updated. Please try again.", "passwordUpdated": "Password updated", "passwordUpdatedDescription": "Your password has been changed successfully.", "passwordUpdateError": "Your password couldn't be updated. Please try again.", "notifications": "Notification Preferences", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive updates about your favorite cats and messages", "adoptionUpdates": "Adoption Updates", "adoptionUpdatesDesc": "Get notified when cats you're interested in are adopted", "comingSoon": "Full settings functionality coming soon. Contact support for account changes."}, "favorites": "Favorites", "favoritesDescription": "Your favorite cats and saved listings", "listedCatsDescription": "Manage your listed cats for adoption", "servicesDescription": "Manage the services your clinic offers", "clinicOverview": "Clinic Overview", "clinicOverviewDescription": "View your clinic's dashboard and key metrics", "clinicProfileManagement": "Clinic Profile", "clinicProfileManagementDescription": "Manage your clinic's information and settings", "clinicSchedule": "Schedule & Hours", "clinicScheduleDescription": "Manage operating hours and availability", "favorite": "Favorite", "rescuer": "Rescuer", "clinic": "Clinic", "adopter": "Adopter", "viewProfile": "View Profile", "healthInformation": "Health Information", "vaccinated": "Vaccinated", "notVaccinated": "Not Vaccinated", "neutered": "Neutered/Spayed", "notNeutered": "Not Neutered/Spayed", "specialNeeds": "Special Needs", "story": "Story", "adoption": "Adoption", "adoptNow": "<PERSON><PERSON><PERSON>", "today": "today", "notFound": "Cat Not Found", "notFoundDescription": "The cat you're looking for could not be found.", "listedCats": "My Cats", "myListedCats": "My Listed Cats", "myFavoriteCats": "My Favorite Cats", "addCat": "Add Cat", "editProfile": "Edit Profile", "dashboard": "Dashboard", "services": "Services", "clinicServices": "Clinic Services", "manageServices": "Manage Services", "addService": "Add Service", "serviceAvailability": {"daily": "Available daily", "appointment": "By appointment only"}, "accountSettings": "Account <PERSON><PERSON>", "joined": "Joined", "publishCat": "Publish a Cat", "startAddingCats": "Start by adding cats that need adoption.", "catDeleted": "Cat listing deleted", "catDeletedDescription": "The cat listing has been removed.", "errorDeletingCat": "Error deleting cat", "errorDeletingCatDescription": "Failed to delete the cat listing. Please try again later.", "errorUpdatingStatusDescription": "Failed to update the cat status. Please try again later.", "confirmDelete": "Delete Cat Listing", "confirmDeleteDescription": "Are you sure you want to delete the listing for {catName}? This action cannot be undone.", "deleting": "Deleting...", "noFavoriteCats": "No favorite cats yet", "noFavoritesYet": "No favorites yet", "startAddingFavorites": "Start adding cats to your favorites to see them here.", "errorLoadingFavorites": "Error loading favorites", "removeFromFavorites": "Remove from favorites", "addToFavorites": "Add to favorites", "favoriteAdded": "Added to favorites", "favoriteAddedDescription": "Cat has been added to your favorites.", "favoriteRemoved": "Removed from favorites", "favoriteRemovedDescription": "Cat has been removed from your favorites.", "noCatsListed": "No cats listed yet", "startListingCats": "Start listing cats for adoption to see them here.", "conversationWith": "Conversation with", "conversations": "conversations", "clinicProfile": {"title": "Clinic Profile", "info": "Your clinic's public information", "verified": "Verified", "website": "Website", "services": "Services"}, "partners": {"title": "Partner Rescuers", "description": "Rescuers you work with", "noPartners": "No partner rescuers yet", "addPartner": "Add Partner"}, "public": {"title": "Profile", "verified": "Verified", "topHelper": "Top Helper", "sendMessage": "Send Message", "follow": "Follow", "following": "Following", "unfollow": "Unfollow", "memberSince": "Member since", "locationNotSpecified": "Location not specified", "stats": {"catsListed": "Cats Listed", "adoptions": "Adoptions", "reviews": "Reviews", "rating": "Rating"}, "tabs": {"listedCats": "Listed Cats", "reviews": "Reviews", "about": "About"}, "listedCats": {"title": "Listed Cats", "count": "{count} cats", "available": "Available", "adopted": "Adopted", "noListedCats": "No cats listed yet", "noListedCatsDescription": "This user hasn't listed any cats for adoption yet."}, "reviews": {"title": "Reviews", "count": "{count} reviews", "rating": "{rating} out of 5", "basedOn": "Based on {count} reviews", "writeReview": "Write a Review", "noReviews": "No reviews yet", "noReviewsDescription": "This user hasn't received any reviews yet.", "helpful": "found this helpful", "markHelpful": "<PERSON> as Helpful", "reviewBy": "Review by", "anonymous": "Anonymous"}, "about": {"title": "About", "bio": "Bio", "location": "Location", "memberSince": "Member since", "role": "Role", "noBio": "No bio available", "noBioDescription": "This user hasn't added a bio yet.", "activitySummary": "Activity Summary", "stats": {"catsListed": "Cats Listed", "successfulAdoptions": "Successful Adoptions", "averageRating": "Average Rating", "reviewsReceived": "Reviews Received"}}, "roles": {"adopter": "Cat Adopter", "rescuer": "Cat Rescuer", "clinic": "Veterinary Clinic", "admin": "Administrator"}, "notFound": {"title": "User Not Found", "description": "The user profile you're looking for could not be found.", "backToHome": "Back to Home"}}, "messages": {"title": "Messages", "description": "Your conversations", "selectConversation": "Select a conversation", "chooseToStart": "Choose a conversation from the sidebar to start chatting", "noMessagesYet": "No messages yet", "noMessagesDescription": "You don't have any messages yet", "noMessagesStartConversation": "Start a conversation with a cat owner", "searchPlaceholder": "Search conversations...", "noMatchingConversations": "No matching conversations", "unknownUser": "Unknown user", "you": "You", "justNow": "Just now", "minutesAgo": "{minutes} minutes ago", "hoursAgo": "{hours} hours ago", "daysAgo": "{days} days ago", "weeksAgo": "{weeks} weeks ago", "viewAllMessages": "View all messages", "startConversation": "Start the conversation", "sendToBegin": "Send a message to begin chatting with", "conversationWith": "Conversation with", "conversations": "conversations", "comingSoon": "Messages functionality coming soon.", "conversationAbout": "This conversation is about", "chatWith": "Chat with", "with": "with", "typePlaceholder": "Type a message...", "regarding": "Re:", "online": "Online", "lastSeen": "Last seen", "verified": "Verified", "safetyNotice": "Your safety is important. Never share personal information outside the platform.", "new": "New"}, "roles": {"admin": "Admin", "rescuer": "Rescuer", "clinic": "Clinic", "adopter": "Adopter"}, "recommended": {"title": "Recommended Cats", "description": "Cats you might be interested in"}}, "forms": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "submit": "<PERSON><PERSON>", "submitting": "Logging in...", "forgotPassword": "Forgot password?", "noAccount": "Don't have an account?", "register": "Register", "orContinueWith": "Or continue with", "successTitle": "Login successful!", "successMessage": "You are now logged in.", "errorTitle": "<PERSON><PERSON> failed", "errorMessage": "There was a problem logging in. Please try again.", "invalidCredentials": "Invalid email or password.", "githubError": "There was a problem logging in with GitHub.", "googleError": "There was a problem logging in with Google.", "description": "Enter your email and password to access your account"}, "register": {"title": "Create Account", "name": "Full Name", "namePlaceholder": "Your name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "submit": "Create account", "submitting": "Creating account...", "hasAccount": "Already have an account?", "login": "<PERSON><PERSON>", "successTitle": "Registration successful!", "successMessage": "Your account has been created.", "errorTitle": "Registration failed", "errorMessage": "There was a problem creating your account.", "tryAgainMessage": "There was a problem creating your account. Please try again.", "passwordRequirement": "Must be at least 8 characters", "roleLabel": "I am a", "rolePlaceholder": "Select your role", "roleAdopter": "Potential Adopter", "roleRescuer": "Rescuer / Shelter", "roleClinic": "Veterinary Clinic", "roleDescription": "This determines what you can do on the platform", "description": "Join our community to find or list cats for adoption"}, "catForm": {"newTitle": "Add New Cat", "editTitle": "Edit Cat Profile", "steps": {"info": "Cat Info", "images": "Images"}, "info": {"basicInfo": "Basic Information", "name": "Name", "namePlaceholder": "<PERSON>'s name", "age": "Age", "agePlaceholder": "e.g. 2 years, 6 months", "gender": "Gender", "male": "Male", "female": "Female", "breed": "Breed", "breedPlaceholder": "Select a breed", "loadingBreeds": "Loading breeds...", "description": "Description", "descriptionPlaceholder": "Describe the cat's personality, habits, etc.", "story": "Story (Optional)", "storyPlaceholder": "Share the cat's backstory or rescue story", "location": "Location", "wilaya": "<PERSON><PERSON><PERSON>", "wilayaPlaceholder": "Select wilaya", "loadingWilayas": "Loading wilayas...", "commune": "<PERSON><PERSON><PERSON>", "communePlaceholder": "Select commune", "selectWilayaFirst": "Select wilaya first", "loadingCommunes": "Loading communes...", "health": "Health & Status", "vaccinated": "Vaccinated", "neutered": "Neutered/Spayed", "specialNeeds": "Special Needs", "adopted": "Already Adopted", "adoptedDescription": "<PERSON> if the cat has already been adopted", "saveAsDraft": "This will save the cat as a draft (not visible to others)", "nextStep": "Next: Images", "vaccinatedDescription": "<PERSON> has received basic vaccinations", "neuteredDescription": "Cat has been neutered or spayed", "specialNeedsDescription": "Cat has special medical or behavioral needs", "specialNeedsPlaceholder": "Describe the special needs", "storyDescription": "A compelling story can help the cat get adopted faster.", "descriptionDescription": "This will be displayed on the cat's profile."}, "images": {"title": "Cat Photos", "description": "Upload up to 5 high-quality photos of the cat. Click the star icon to set the primary photo.", "maxReached": "Maximum of 5 images reached. Remove an image if you want to add a different one.", "dropzone": "Drag & drop images here, or click to select files", "fileRequirements": "(Max 5 images, 5MB each, JPG, PNG or WebP)", "setPrimary": "Set as primary image", "remove": "Remove image", "primary": "Primary", "add": "Add image", "savePrompt": "Save your changes or publish the cat profile when you're ready.", "noImages": "Please add at least one image of the cat", "tips": {"title": "Image Tips", "tip1": "Use clear, well-lit photos that show the cat clearly", "tip2": "Include at least one photo of the cat's face", "tip3": "Add photos that highlight unique features or personality", "tip4": "Choose photos that are already properly framed and focused"}, "back": "Back to Info"}, "buttons": {"saveDraft": "Save as Draft", "saving": "Saving...", "publish": "Publish Cat", "publishing": "Publishing...", "update": "Update Cat", "updating": "Updating...", "back": "Back"}, "validation": {"nameRequired": "Name is required", "nameLength": "Name must be at least 2 characters", "ageRequired": "Age is required", "ageMax": "Age must be 25 years or less", "breedRequired": "Breed is required", "descriptionRequired": "Description is required", "descriptionLength": "Description must be at least 20 characters", "wilayaRequired": "Wilaya is required", "communeRequired": "Commune is required", "specialNeedsDescriptionRequired": "Please describe the special needs"}, "errors": {"imageSize": "Image size exceeds 5MB limit", "imageFormat": "Invalid image format. Please use JPG, PNG or WebP", "uploadFailed": "Failed to upload image", "maxImages": "Maximum 5 images allowed"}, "success": {"draftSaved": "Draft saved successfully", "published": "Cat published successfully", "updated": "Cat updated successfully"}}}, "theme": {"toggle": "Toggle theme", "light": "Light", "dark": "Dark", "system": "System"}, "language": {"switchLanguage": "Switch language", "english": "English", "french": "French", "arabic": "Arabic"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page", "of": "of", "showing": "Showing", "to": "to", "of_total": "of", "results": "results", "firstPage": "First Page", "lastPage": "Last Page", "morePages": "More pages", "aria": {"previous": "Go to previous page", "next": "Go to next page", "page": "Go to page"}}, "buttons": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "send": "Send", "submit": "Submit", "message": "Message", "loading": "Loading..."}, "stats": {"users": "Registered Users", "errors": {"failedToLoad": "Failed to load statistics", "noStats": "No statistics available"}}, "chat": {"successTitle": "Chat started!", "successDescription": "You can now communicate with the cat owner.", "errorTitle": "Something went wrong", "errorDescription": "Unable to start chat. Please try again.", "continueConversation": "Continue Conversation", "continueConversationWith": "Continue chat with {name}", "contactRescuer": "Contact Rescuer", "startConversation": "Start a conversation", "dialogDescription": "Send a message to the cat owner to ask questions or express interest.", "messagePlaceholder": "Type your message here...", "sending": "Sending...", "sendMessage": "Send Message"}, "activity": {"youFavorited": "You favorited", "conversationAbout": "Conversation about", "sentYouMessage": "sent you a message"}, "admin": {"title": "Admin Dashboard", "description": "Manage users, cats, and site content", "welcome": "Welcome, {name}", "tabs": {"overview": "Overview", "cats": "Cats", "users": "Users", "clinics": "Clinics"}, "stats": {"title": "Overview", "totalUsers": "Total Users", "totalCats": "Total Cats", "totalMessages": "Total Messages", "totalFavorites": "Total Favorites", "thisWeek": "this week", "adoptionStatistics": "Adoption Statistics", "catStatusOverview": "Cat Status Overview", "monthly": "Monthly", "weekly": "Weekly", "weeklyDataNotAvailable": "Weekly data not available in demo", "status": {"available": "Available", "pending": "Pending", "adopted": "Adopted", "unavailable": "Unavailable"}}, "recentCats": {"title": "Recent Cat Listings", "description": "The most recently added cats"}, "recentUsers": {"title": "Recent User Registrations", "description": "The most recently registered users"}, "cats": {"title": "Manage Cat Listings", "description": "View, approve, edit, or remove cat listings", "searchPlaceholder": "Search cats...", "noCats": "No cats found", "columns": {"cat": "Cat", "owner": "Owner", "location": "Location", "status": "Status", "createdAt": "Created", "yearsOld": "years old"}, "filters": {"status": "Status", "allStatuses": "All Statuses"}, "status": {"available": "Available", "pending": "Pending", "adopted": "Adopted", "unavailable": "Unavailable"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete", "changeStatus": "Change Status"}, "dialogs": {"deleteTitle": "Delete Cat", "deleteDescription": "Are you sure you want to delete this cat? This action cannot be undone.", "statusTitle": "Change Cat Status", "statusDescription": "Select the new status for this cat."}, "changeStatus": "Change Cat Status", "changeStatusDescription": "Change the status of {name}", "deleteCat": "Delete Cat", "deleteCatDescription": "Are you sure you want to delete {name}? This action cannot be undone."}, "users": {"title": "Manage Users", "description": "View and manage user accounts", "searchPlaceholder": "Search users...", "noUsers": "No users found", "roleUpdated": "Role updated", "roleUpdatedDescription": "The role of {name} has been updated.", "columns": {"user": "User", "email": "Email", "role": "Role", "location": "Location", "createdAt": "Joined"}, "table": {"user": "User", "role": "Role", "location": "Location", "joinedDate": "Joined Date"}, "filters": {"role": "Role", "allRoles": "All Roles"}, "actions": {"view": "View Profile", "changeRole": "Change Role", "delete": "Delete User"}, "dialogs": {"deleteTitle": "Delete User", "deleteDescription": "Are you sure you want to delete this user? This action cannot be undone.", "roleTitle": "Change User Role", "roleDescription": "Select the new role for this user."}, "changeRole": "Change User Role", "changeRoleDescription": "Change the role of {name}", "deleteUser": "Delete User", "deleteUserDescription": "Are you sure you want to delete {name}? This action cannot be undone."}, "clinics": {"title": "Manage Clinics", "description": "View and manage veterinary clinic profiles", "searchPlaceholder": "Search clinics...", "noClinics": "No clinics found", "featureNotAvailable": "Feature not available yet", "columns": {"clinic": "Clinic", "location": "Location", "contact": "Contact", "status": "Status", "actions": "Actions"}, "actions": {"viewProfile": "View Profile", "edit": "Edit", "verify": "Verify", "unverify": "Unverify", "delete": "Delete"}, "status": {"verified": "Verified", "unverified": "Unverified", "pending": "Pending", "rejected": "Rejected"}, "filters": {"allStatuses": "All Statuses", "allClinics": "All Clinics", "activeOnly": "Active Only", "inactiveOnly": "Inactive Only", "newestFirst": "Newest First", "oldestFirst": "Oldest First", "nameAZ": "Name A-Z", "nameZA": "Name Z-A", "highestRated": "Highest Rated", "verificationStatus": "Verification Status"}, "verification": {"verifyTitle": "Verify Clinic", "verifyDescription": "Are you sure you want to verify this clinic? This will mark it as verified and make it visible to users.", "rejectTitle": "Reject Clinic", "rejectDescription": "Are you sure you want to reject this clinic? This will mark it as rejected and hide it from users.", "deleteTitle": "Delete Clinic", "deleteDescription": "Are you sure you want to delete this clinic? This action cannot be undone and will permanently remove all clinic data.", "verifyButton": "Verify", "rejectButton": "Reject", "deleteButton": "Delete", "cancelButton": "Cancel"}}, "roles": {"admin": "Administrator", "rescuer": "Rescuer", "clinic": "Clinic", "adopter": "Adopter"}, "status": {"user": {"admin": "Administrator", "rescuer": "Rescuer", "clinic": "Clinic", "adopter": "Adopter"}, "cat": {"available": "Available", "pending": "Pending", "adopted": "Adopted", "unavailable": "Unavailable"}, "application": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected"}}, "table": {"actions": "Actions", "showing": "Showing {start} to {end} of {total} results", "previous": "Previous", "next": "Next", "page": "Page {current} of {total}"}, "filters": {"all": "All"}, "common": {"error": "Error", "cancel": "Cancel", "update": "Update", "updating": "Updating...", "delete": "Delete", "deleting": "Deleting...", "notSpecified": "Not specified"}}, "breeds": {"Persian": "Persian", "Siamese": "Siamese", "Maine Coon": "Maine Coon", "Ragdoll": "<PERSON><PERSON><PERSON><PERSON>", "Bengal": "Bengal", "Sphynx": "<PERSON><PERSON><PERSON>", "British Shorthair": "British Shorthair", "Abyssinian": "Abyssinian", "Scottish Fold": "Scottish Fold", "Norwegian Forest": "Norwegian Forest", "Siberian": "Siberian", "Russian Blue": "Russian Blue", "Birman": "<PERSON><PERSON><PERSON>", "Domestic Shorthair": "Domestic Shorthair", "Domestic Longhair": "Domestic Longhair", "Mixed Breed": "Mixed Breed"}, "notFound": {"title": "Page Not Found", "subtitle": "Oops! This page seems to have wandered off like a curious cat.", "description": "The page you're looking for doesn't exist. It might have been moved, deleted, or you may have mistyped the URL.", "suggestions": {"title": "Here's what you can do:", "browseCats": "Browse available cats for adoption", "goHome": "Return to the homepage"}, "buttons": {"browseCats": "Browse Cats", "goHome": "Go Home"}}, "clinic": {"registration": {"title": "Clinic Registration", "subtitle": "Join our network of trusted veterinary clinics", "description": "Register your clinic to connect with cat owners and provide essential veterinary services.", "formTitle": "Register Your Clinic", "formDescription": "Please provide your clinic information to get started.", "hasAccount": "Already have an account?", "step": "Step {current} of {total}", "previous": "Previous", "next": "Next", "submit": "Submit Registration", "submitting": "Submitting...", "successTitle": "Registration Submitted", "successMessage": "Your clinic registration has been submitted for review. You will receive an email confirmation shortly.", "errorTitle": "Registration Failed", "steps": {"basicInfo": {"title": "Basic Information", "description": "Description", "name": "Clinic Name", "namePlaceholder": "Enter your clinic name", "nameDescription": "The official name of your veterinary clinic", "email": "Email Address", "emailPlaceholder": "<EMAIL>", "emailDescription": "This will be your login email and primary contact", "password": "Password", "passwordDescription": "Must be at least 8 characters long", "phone": "Phone Number", "phonePlaceholder": "+213 XXX XXX XXX", "phoneDescription": "Primary contact number for your clinic", "website": "Website", "websitePlaceholder": "https://www.yourclinic.com", "websiteDescription": "Your clinic's website (optional)", "descriptionPlaceholder": "Describe your clinic, services, and what makes you special...", "descriptionDescription": "Tell potential clients about your clinic (optional)"}, "location": {"title": "Location & Service Area", "description": "Where is your clinic located?", "address": "Street Address", "addressPlaceholder": "123 Main Street", "addressDescription": "Your clinic's physical address", "city": "City", "cityPlaceholder": "Algiers", "state": "State/Province", "statePlaceholder": "Algiers", "postalCode": "Postal Code", "postalCodePlaceholder": "16000", "serviceAreaRadius": "Service Area Radius", "serviceAreaDescription": "How far from your clinic do you provide services?", "coordinates": "GPS Coordinates", "coordinatesDescription": "Optional: Provide exact coordinates for better location accuracy", "latitude": "Latitude", "longitude": "Longitude", "useCurrentLocation": "Use my current location"}, "services": {"title": "Services & Specializations", "description": "What services do you offer?", "specializations": "Specializations", "specializationsDescription": "Select the services and specializations your clinic offers", "commonSpecializations": "Common Specializations", "customSpecialization": "Add Custom Specialization", "customSpecializationPlaceholder": "Enter custom specialization", "selectedSpecializations": "Selected Specializations", "amenities": "Amenities & Facilities", "amenitiesDescription": "What amenities and facilities does your clinic have?", "commonAmenities": "Common Amenities", "customAmenity": "Add Custom Amenity", "customAmenityPlaceholder": "Enter custom amenity", "selectedAmenities": "Selected Amenities", "servicesNote": "Don't worry if you don't see all your services listed. You can add more detailed services after registration.", "specializationOptions": {"general_practice": "General Practice", "surgery": "Surgery", "dental_care": "Dental Care", "emergency_care": "Emergency Care", "vaccination": "Vaccination", "grooming": "Grooming", "behavioral_therapy": "Behavioral Therapy", "nutrition_counseling": "Nutrition Counseling", "diagnostic_imaging": "Diagnostic Imaging", "laboratory_services": "Laboratory Services", "spay_neuter": "Spay/Neuter", "microchipping": "Microchipping", "wellness_exams": "Wellness Exams", "senior_care": "Senior Care", "exotic_animals": "Exotic Animals"}, "amenityOptions": {"parking": "Parking Available", "wheelchair_accessible": "Wheelchair Accessible", "air_conditioning": "Air Conditioning", "waiting_room": "Waiting Room", "separate_cat_dog_areas": "Separate Cat/Dog Areas", "isolation_rooms": "Isolation Rooms", "surgical_suite": "Surgical Suite", "x_ray_equipment": "X-Ray Equipment", "laboratory": "On-site Laboratory", "pharmacy": "Pharmacy", "grooming_facilities": "Grooming Facilities", "boarding": "Boarding Services", "24_hour_emergency": "24-Hour Emergency", "online_booking": "Online Booking", "telemedicine": "Telemedicine"}}, "verification": {"title": "Images & Social Media", "description": "Add images and social media links to showcase your clinic", "images": "Clinic Images", "imagesDescription": "Add photos of your clinic to help pet owners get familiar with your facility.", "addImageUrl": "Add Image URL", "imageUrlPlaceholder": "https://example.com/image.jpg", "selectedImages": "Selected Images", "imagesNote": "You can add up to 10 images. Make sure the URLs are publicly accessible.", "socialMedia": "Social Media", "socialMediaDescription": "Connect your social media accounts to build trust with pet owners.", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "socialMediaNote": "Social media links are optional but help build credibility with potential clients."}, "review": {"title": "Review Your Information", "description": "Please review all the information before submitting your registration.", "additionalInfo": "Additional Information", "images": "Images", "andMoreImages": "and {count} more images", "socialMedia": "Social Media", "importantNotice": "Important Notice", "reviewNotice": "Your clinic registration will be reviewed by our team. You'll receive an email confirmation once your clinic is approved and ready to start receiving appointments."}}}, "services": {"setupTitle": "Service Setup", "setupDescription": "Add the services your clinic offers to help pet owners find you.", "setupNote": "Don't worry if you don't see all your services listed. You can add more detailed services after registration.", "addService": "Add Service", "addNewService": "Add New Service", "addNewServiceDescription": "Add a new service to your clinic profile to help pet owners find you.", "editService": "Edit Service", "editServiceDescription": "Update the details of your clinic service.", "addedServices": "Added Services", "noServices": "No services added yet", "noServicesDescription": "Start by adding your first service to help pet owners understand what you offer.", "addFirstService": "Add Your First Service", "name": "Service Name", "namePlaceholder": "Enter service name", "nameDescription": "The name of the service you provide", "description": "Description", "descriptionPlaceholder": "Describe what this service includes...", "descriptionDescription": "Detailed description of the service", "category": "Category", "selectCategory": "Select a category", "categoryDescription": "Choose the category that best fits this service", "price": "Price", "priceDescription": "Service price in DZD (optional)", "duration": "Duration", "durationDescription": "Estimated duration in minutes (optional)", "minutes": "min", "requirements": "Requirements", "requirementsDescription": "Any special requirements for this service (optional)", "requirementPlaceholder": "Enter a requirement", "selectedRequirements": "Selected Requirements", "notes": "Additional Notes", "notesPlaceholder": "Any additional information about this service...", "notesDescription": "Additional notes or special instructions", "cancel": "Cancel", "updateService": "Update Service", "categories": {"general_checkup": "General Checkup", "vaccination": "Vaccination", "surgery": "Surgery", "dental_care": "Dental Care", "grooming": "Grooming", "emergency": "Emergency Care", "diagnostic": "Diagnostic", "behavioral": "Behavioral Therapy", "nutrition": "Nutrition Counseling", "other": "Other"}}}}