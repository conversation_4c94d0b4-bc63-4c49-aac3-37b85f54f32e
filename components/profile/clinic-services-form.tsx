"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { api } from "@/lib/trpc/react";
import { useTranslations } from "next-intl";
import { useRouter } from "@/lib/i18n/navigation";

const serviceCategories = [
	{ value: "general_checkup", label: "General Checkup" },
	{ value: "vaccination", label: "Vaccination" },
	{ value: "surgery", label: "Surgery" },
	{ value: "dental_care", label: "Dental Care" },
	{ value: "grooming", label: "Grooming" },
	{ value: "emergency", label: "Emergency" },
	{ value: "diagnostic", label: "Diagnostic" },
	{ value: "behavioral", label: "Behavioral" },
	{ value: "nutrition", label: "Nutrition" },
	{ value: "other", label: "Other" },
];

const serviceSchema = z.object({
	name: z
		.string()
		.min(2, {
			message: "Service name must be at least 2 characters.",
		})
		.max(100, {
			message: "Service name must be less than 100 characters.",
		}),
	description: z
		.string()
		.min(10, {
			message: "Description must be at least 10 characters.",
		})
		.max(1000, {
			message: "Description must be less than 1000 characters.",
		})
		.optional(),
	category: z.enum(
		[
			"general_checkup",
			"vaccination",
			"surgery",
			"dental_care",
			"grooming",
			"emergency",
			"diagnostic",
			"behavioral",
			"nutrition",
			"other",
		],
		{
			required_error: "Please select a service category.",
		}
	),
	price: z
		.number()
		.min(0, {
			message: "Price cannot be negative.",
		})
		.max(10000, {
			message: "Price must be reasonable.",
		})
		.optional(),
	duration: z
		.number()
		.min(5, {
			message: "Duration must be at least 5 minutes.",
		})
		.max(480, {
			message: "Duration cannot exceed 8 hours.",
		})
		.optional(),
	isAvailable: z.boolean().default(true),
	notes: z
		.string()
		.max(500, {
			message: "Notes must be less than 500 characters.",
		})
		.optional(),
});

type ServiceValues = z.infer<typeof serviceSchema>;

interface ClinicServicesFormProps {
	service?: any;
	clinicProfile: any;
	onSuccess?: () => void;
}

export function ClinicServicesForm({
	service,
	clinicProfile,
	onSuccess,
}: ClinicServicesFormProps) {
	const { toast } = useToast();
	const t = useTranslations("profile");
	const router = useRouter();

	const form = useForm<ServiceValues>({
		resolver: zodResolver(serviceSchema),
		defaultValues: {
			name: service?.name || "",
			description: service?.description || "",
			category: service?.category || "general_checkup",
			price: service?.price || undefined,
			duration: service?.duration || undefined,
			isAvailable: service?.isAvailable ?? true,
			notes: service?.notes || "",
		},
	});

	// tRPC mutations
	const createServiceMutation = api.clinicServices.create.useMutation({
		onSuccess: () => {
			toast({
				title: "Service created",
				description: "Your service has been added successfully.",
			});
			if (onSuccess) {
				onSuccess();
			} else {
				router.push("/profile/services");
			}
		},
		onError: (error) => {
			toast({
				title: "Error",
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const updateServiceMutation = api.clinicServices.update.useMutation({
		onSuccess: () => {
			toast({
				title: "Service updated",
				description: "Your service has been updated successfully.",
			});
			if (onSuccess) {
				onSuccess();
			} else {
				router.push("/profile/services");
			}
		},
		onError: (error) => {
			toast({
				title: "Error",
				description: error.message,
				variant: "destructive",
			});
		},
	});

	async function onSubmit(values: ServiceValues) {
		if (!clinicProfile) {
			toast({
				title: "Error",
				description:
					"Clinic profile not found. Please complete your profile first.",
				variant: "destructive",
			});
			return;
		}

		const serviceData = {
			clinicId: clinicProfile.id,
			name: values.name,
			description: values.description,
			category: values.category,
			price: values.price,
			duration: values.duration,
			isAvailable: values.isAvailable,
			notes: values.notes,
		};

		if (service) {
			// Update existing service
			updateServiceMutation.mutate({
				id: service.id,
				...serviceData,
			});
		} else {
			// Create new service
			createServiceMutation.mutate(serviceData);
		}
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle>
					{service ? "Edit Service" : "Add New Service"}
				</CardTitle>
			</CardHeader>
			<Form {...form}>
				<form onSubmit={form.handleSubmit(onSubmit)}>
					<CardContent className="space-y-4">
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Service Name</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder="e.g. Vaccinations, Spay/Neuter, etc."
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Description</FormLabel>
									<FormControl>
										<Textarea
											{...field}
											placeholder="Describe the service you offer..."
											className="resize-none"
											rows={4}
										/>
									</FormControl>
									<FormDescription>
										Provide details about what this service
										includes.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="price"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Price (optional)</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder="e.g. $50, $100-150, or 'Free'"
										/>
									</FormControl>
									<FormDescription>
										Leave blank if price varies or is to be
										discussed.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="isAvailable"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
										<div className="space-y-0.5">
											<FormLabel className="text-base">
												Available
											</FormLabel>
											<FormDescription>
												Show this service as currently
												available
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="requiresAppointment"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
										<div className="space-y-0.5">
											<FormLabel className="text-base">
												Requires Appointment
											</FormLabel>
											<FormDescription>
												This service requires scheduling
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
									</FormItem>
								)}
							/>
						</div>
					</CardContent>
					<CardFooter className="flex justify-between">
						<Button
							type="button"
							variant="outline"
							onClick={() => form.reset()}
						>
							Cancel
						</Button>
						<Button type="submit" disabled={isSubmitting}>
							{isSubmitting
								? "Saving..."
								: service
									? "Update Service"
									: "Add Service"}
						</Button>
					</CardFooter>
				</form>
			</Form>
		</Card>
	);
}
