"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Plus, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { ServiceData, SERVICE_CATEGORIES, ServiceCategory } from "./service-setup-wizard";

// Validation schema
const serviceFormSchema = z.object({
	name: z.string().min(2, "Service name must be at least 2 characters").max(100),
	description: z.string().min(10, "Description must be at least 10 characters").max(1000).optional(),
	category: z.enum(SERVICE_CATEGORIES),
	price: z.number().min(0, "Price cannot be negative").max(10000).optional(),
	duration: z.number().min(5, "Duration must be at least 5 minutes").max(480).optional(),
	requirements: z.array(z.string()).optional(),
	notes: z.string().max(500).optional(),
});

type ServiceFormData = z.infer<typeof serviceFormSchema>;

interface ServiceFormProps {
	initialData?: ServiceData;
	onSubmit: (data: ServiceData) => void;
	onCancel: () => void;
}

export function ServiceForm({ initialData, onSubmit, onCancel }: ServiceFormProps) {
	const t = useTranslations("clinic.services");
	const [newRequirement, setNewRequirement] = useState("");

	const form = useForm<ServiceFormData>({
		resolver: zodResolver(serviceFormSchema),
		defaultValues: {
			name: initialData?.name || "",
			description: initialData?.description || "",
			category: initialData?.category || "general_checkup",
			price: initialData?.price || undefined,
			duration: initialData?.duration || undefined,
			requirements: initialData?.requirements || [],
			notes: initialData?.notes || "",
		},
	});

	const requirements = form.watch("requirements") || [];

	// Add requirement
	const addRequirement = () => {
		if (newRequirement.trim() && !requirements.includes(newRequirement.trim())) {
			form.setValue("requirements", [...requirements, newRequirement.trim()]);
			setNewRequirement("");
		}
	};

	// Remove requirement
	const removeRequirement = (requirement: string) => {
		form.setValue(
			"requirements",
			requirements.filter((req) => req !== requirement)
		);
	};

	// Handle form submission
	const handleSubmit = (data: ServiceFormData) => {
		onSubmit(data);
	};

	// Get category display name
	const getCategoryName = (category: ServiceCategory) => {
		return t(`categories.${category}`);
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
				{/* Basic Information */}
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{/* Service Name */}
					<FormField
						control={form.control}
						name="name"
						render={({ field }) => (
							<FormItem>
								<FormLabel className="required">{t("name")}</FormLabel>
								<FormControl>
									<Input placeholder={t("namePlaceholder")} {...field} />
								</FormControl>
								<FormDescription>{t("nameDescription")}</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Category */}
					<FormField
						control={form.control}
						name="category"
						render={({ field }) => (
							<FormItem>
								<FormLabel className="required">{t("category")}</FormLabel>
								<Select onValueChange={field.onChange} defaultValue={field.value}>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder={t("selectCategory")} />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{SERVICE_CATEGORIES.map((category) => (
											<SelectItem key={category} value={category}>
												{getCategoryName(category)}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormDescription>{t("categoryDescription")}</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* Description */}
				<FormField
					control={form.control}
					name="description"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{t("description")}</FormLabel>
							<FormControl>
								<Textarea
									placeholder={t("descriptionPlaceholder")}
									className="min-h-[100px]"
									{...field}
								/>
							</FormControl>
							<FormDescription>{t("descriptionDescription")}</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Price and Duration */}
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{/* Price */}
					<FormField
						control={form.control}
						name="price"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{t("price")}</FormLabel>
								<FormControl>
									<div className="relative">
										<Input
											type="number"
											placeholder="0"
											{...field}
											onChange={(e) => {
												const value = e.target.value;
												field.onChange(value ? parseFloat(value) : undefined);
											}}
											value={field.value || ""}
										/>
										<span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
											DZD
										</span>
									</div>
								</FormControl>
								<FormDescription>{t("priceDescription")}</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Duration */}
					<FormField
						control={form.control}
						name="duration"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{t("duration")}</FormLabel>
								<FormControl>
									<div className="relative">
										<Input
											type="number"
											placeholder="30"
											{...field}
											onChange={(e) => {
												const value = e.target.value;
												field.onChange(value ? parseInt(value) : undefined);
											}}
											value={field.value || ""}
										/>
										<span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
											{t("minutes")}
										</span>
									</div>
								</FormControl>
								<FormDescription>{t("durationDescription")}</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* Requirements */}
				<div className="space-y-4">
					<div>
						<FormLabel>{t("requirements")}</FormLabel>
						<FormDescription>{t("requirementsDescription")}</FormDescription>
					</div>

					{/* Add Requirement */}
					<div className="flex gap-2">
						<Input
							placeholder={t("requirementPlaceholder")}
							value={newRequirement}
							onChange={(e) => setNewRequirement(e.target.value)}
							onKeyPress={(e) => {
								if (e.key === "Enter") {
									e.preventDefault();
									addRequirement();
								}
							}}
						/>
						<Button
							type="button"
							variant="outline"
							size="icon"
							onClick={addRequirement}
							disabled={!newRequirement.trim()}
						>
							<Plus className="h-4 w-4" />
						</Button>
					</div>

					{/* Requirements List */}
					{requirements.length > 0 && (
						<div className="space-y-2">
							<p className="text-sm font-medium">{t("selectedRequirements")}</p>
							<div className="flex flex-wrap gap-2">
								{requirements.map((requirement, index) => (
									<Badge key={index} variant="secondary" className="flex items-center gap-1">
										{requirement}
										<Button
											type="button"
											variant="ghost"
											size="sm"
											className="h-auto p-0 text-muted-foreground hover:text-destructive"
											onClick={() => removeRequirement(requirement)}
										>
											<X className="h-3 w-3" />
										</Button>
									</Badge>
								))}
							</div>
						</div>
					)}
				</div>

				{/* Notes */}
				<FormField
					control={form.control}
					name="notes"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{t("notes")}</FormLabel>
							<FormControl>
								<Textarea
									placeholder={t("notesPlaceholder")}
									className="min-h-[80px]"
									{...field}
								/>
							</FormControl>
							<FormDescription>{t("notesDescription")}</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Form Actions */}
				<div className="flex justify-end gap-3">
					<Button type="button" variant="outline" onClick={onCancel}>
						{t("cancel")}
					</Button>
					<Button type="submit">
						{initialData ? t("updateService") : t("addService")}
					</Button>
				</div>
			</form>
		</Form>
	);
}
