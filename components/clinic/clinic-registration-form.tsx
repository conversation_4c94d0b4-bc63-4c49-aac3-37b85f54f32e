"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Form } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { useRouter as useIntlRouter } from "@/lib/i18n/navigation";
import { signUp } from "@/lib/auth/client";
import { api } from "@/lib/trpc/react";
import { sendClinicRegistrationEmails } from "@/lib/services/clinic-email-service";

// Import step components
import { BasicInfoStep } from "./registration-steps/basic-info-step";
import { LocationStep } from "./registration-steps/location-step";
import { ServicesStep } from "./registration-steps/services-step";
import { VerificationStep } from "./registration-steps/verification-step";
import { ReviewStep } from "./registration-steps/review-step";
// import { ServiceSetupWizard, ServiceData } from "./service-setup-wizard";
// Note: VerificationStep and ReviewStep will be created next

// Registration steps enum
export enum RegistrationStep {
	BASIC_INFO = 0,
	LOCATION = 1,
	SERVICES = 2,
	VERIFICATION = 3,
	REVIEW = 4,
}

// Complete registration schema
const clinicRegistrationSchema = z.object({
	// Basic Info
	name: z.string().min(2, "Name must be at least 2 characters").max(100),
	email: z.string().email("Invalid email format"),
	password: z.string().min(8, "Password must be at least 8 characters"),
	phone: z.string().min(10, "Phone must be at least 10 characters").max(20),
	description: z
		.string()
		.min(10, "Description must be at least 10 characters")
		.max(2000)
		.optional(),
	website: z.string().url("Invalid website URL").optional(),

	// Location
	address: z
		.string()
		.min(5, "Address must be at least 5 characters")
		.max(200),
	city: z.string().min(2, "City must be at least 2 characters").max(50),
	state: z.string().min(2, "State must be at least 2 characters").max(50),
	postalCode: z
		.string()
		.min(3, "Postal code must be at least 3 characters")
		.max(10)
		.optional(),
	coordinates: z
		.object({
			lat: z.number().min(-90).max(90),
			lng: z.number().min(-180).max(180),
		})
		.optional(),
	serviceAreaRadius: z.number().min(1).max(100).optional(),

	// Services
	specializations: z.array(z.string()).max(15).optional(),
	amenities: z.array(z.string()).max(20).optional(),
	operatingHours: z
		.record(
			z.string(),
			z.object({
				open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
				close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
				closed: z.boolean().default(false),
			})
		)
		.optional(),
	emergencyHours: z
		.record(
			z.string(),
			z.object({
				open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
				close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
				available: z.boolean().default(false),
			})
		)
		.optional(),

	// Verification
	images: z.array(z.string().url()).max(10).optional(),
	socialMedia: z
		.object({
			facebook: z.string().url().optional(),
			instagram: z.string().url().optional(),
			twitter: z.string().url().optional(),
		})
		.optional(),
});

export type ClinicRegistrationData = z.infer<typeof clinicRegistrationSchema>;

const stepTitles = [
	"basicInfo",
	"location",
	"services",
	"verification",
	"review",
] as const;

export function ClinicRegistrationForm() {
	const [currentStep, setCurrentStep] = useState(RegistrationStep.BASIC_INFO);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [services, setServices] = useState<any[]>([]);
	const intlRouter = useIntlRouter();
	const { toast } = useToast();
	const t = useTranslations("clinic.registration");
	const errorsT = useTranslations("errors");

	const form = useForm<ClinicRegistrationData>({
		resolver: zodResolver(clinicRegistrationSchema),
		defaultValues: {
			name: "",
			email: "",
			password: "",
			phone: "",
			description: "",
			website: "",
			address: "",
			city: "",
			state: "",
			postalCode: "",
			specializations: [],
			amenities: [],
			images: [],
			socialMedia: {
				facebook: "",
				instagram: "",
				twitter: "",
			},
		},
		mode: "onChange",
	});

	// Calculate progress
	const progress = ((currentStep + 1) / stepTitles.length) * 100;

	// Navigation functions
	const nextStep = () => {
		if (currentStep < stepTitles.length - 1) {
			setCurrentStep(currentStep + 1);
		}
	};

	const prevStep = () => {
		if (currentStep > 0) {
			setCurrentStep(currentStep - 1);
		}
	};

	// Validate current step
	const validateCurrentStep = async () => {
		const values = form.getValues();

		switch (currentStep) {
			case RegistrationStep.BASIC_INFO:
				return await form.trigger([
					"name",
					"email",
					"password",
					"phone",
				]);
			case RegistrationStep.LOCATION:
				return await form.trigger(["address", "city", "state"]);
			case RegistrationStep.SERVICES:
				// Services are optional, so always valid
				return true;
			case RegistrationStep.VERIFICATION:
				// Verification is optional, so always valid
				return true;
			case RegistrationStep.REVIEW:
				return await form.trigger();
			default:
				return false;
		}
	};

	// Handle next step
	const handleNext = async () => {
		const isValid = await validateCurrentStep();
		if (isValid) {
			nextStep();
		}
	};

	// Submit form
	const onSubmit = async (values: ClinicRegistrationData) => {
		setIsSubmitting(true);

		try {
			// First, create the user account
			const { error: authError } = await signUp.email({
				name: values.name,
				email: values.email,
				password: values.password,
				callbackURL: `/auth/login`,
			});

			if (authError) {
				toast({
					title: t("errorTitle"),
					description:
						authError.message || errorsT("registrationFailed"),
					variant: "destructive",
				});
				return;
			}

			// TODO: After user verification, create clinic profile and send emails
			// This will be handled in a separate API endpoint that gets called
			// after email verification is complete

			// Show success message
			toast({
				title: t("successTitle"),
				description: t("successMessage"),
			});

			// Redirect to login with a message about email verification
			intlRouter.push("/auth/login?message=verify-email");
		} catch (error) {
			console.error("Registration error:", error);
			toast({
				title: t("errorTitle"),
				description: errorsT("tryAgainMessage"),
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	// Render current step
	const renderStep = () => {
		switch (currentStep) {
			case RegistrationStep.BASIC_INFO:
				return <BasicInfoStep form={form} />;
			case RegistrationStep.LOCATION:
				return <LocationStep form={form} />;
			case RegistrationStep.SERVICES:
				return (
					<ServicesStep
						form={form}
						services={services}
						onServicesChange={setServices}
					/>
				);
			case RegistrationStep.VERIFICATION:
				return <VerificationStep form={form} />;
			case RegistrationStep.REVIEW:
				return <ReviewStep form={form} />;
			default:
				return null;
		}
	};

	return (
		<div className="space-y-6">
			{/* Progress Bar */}
			<div className="space-y-2">
				<div className="flex justify-between text-sm text-muted-foreground">
					<span>
						{t("step", {
							current: currentStep + 1,
							total: stepTitles.length,
						})}
					</span>
					<span>{Math.round(progress)}%</span>
				</div>
				<Progress value={progress} className="h-2" />
				<div className="text-center">
					<h3 className="text-lg font-semibold">
						{t(`steps.${stepTitles[currentStep]}.title`)}
					</h3>
					<p className="text-sm text-muted-foreground">
						{t(`steps.${stepTitles[currentStep]}.description`)}
					</p>
				</div>
			</div>

			{/* Step Content */}
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="space-y-6"
				>
					{renderStep()}

					{/* Navigation Buttons */}
					<div className="flex justify-between pt-6">
						<Button
							type="button"
							variant="outline"
							onClick={prevStep}
							disabled={currentStep === 0}
						>
							{t("previous")}
						</Button>

						{currentStep === stepTitles.length - 1 ? (
							<Button
								type="submit"
								disabled={isSubmitting}
								className="min-w-[120px]"
							>
								{isSubmitting ? t("submitting") : t("submit")}
							</Button>
						) : (
							<Button
								type="button"
								onClick={handleNext}
								className="min-w-[120px]"
							>
								{t("next")}
							</Button>
						)}
					</div>
				</form>
			</Form>
		</div>
	);
}
