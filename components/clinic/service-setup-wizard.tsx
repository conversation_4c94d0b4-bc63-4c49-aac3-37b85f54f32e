"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Edit3, Check, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { ServiceForm } from "./service-form";

// Service categories with translations
export const SERVICE_CATEGORIES = [
	"general_checkup",
	"vaccination",
	"surgery",
	"dental_care",
	"grooming",
	"emergency",
	"diagnostic",
	"behavioral",
	"nutrition",
	"other",
] as const;

export type ServiceCategory = (typeof SERVICE_CATEGORIES)[number];

// Service data structure
export interface ServiceData {
	id?: string; // Temporary ID for editing
	name: string;
	description?: string;
	category: ServiceCategory;
	price?: number;
	duration?: number;
	requirements?: string[];
	notes?: string;
}

// Validation schema for service
const serviceSchema = z.object({
	name: z.string().min(2, "Service name must be at least 2 characters").max(100),
	description: z.string().min(10, "Description must be at least 10 characters").max(1000).optional(),
	category: z.enum(SERVICE_CATEGORIES),
	price: z.number().min(0, "Price cannot be negative").max(10000).optional(),
	duration: z.number().min(5, "Duration must be at least 5 minutes").max(480).optional(),
	requirements: z.array(z.string()).optional(),
	notes: z.string().max(500).optional(),
});

interface ServiceSetupWizardProps {
	onServicesChange: (services: ServiceData[]) => void;
	initialServices?: ServiceData[];
}

export function ServiceSetupWizard({
	onServicesChange,
	initialServices = [],
}: ServiceSetupWizardProps) {
	const t = useTranslations("clinic.services");
	const [services, setServices] = useState<ServiceData[]>(initialServices);
	const [editingService, setEditingService] = useState<ServiceData | null>(null);
	const [showForm, setShowForm] = useState(false);

	// Handle adding a new service
	const handleAddService = (serviceData: ServiceData) => {
		const newService = {
			...serviceData,
			id: Date.now().toString(), // Temporary ID
		};
		const updatedServices = [...services, newService];
		setServices(updatedServices);
		onServicesChange(updatedServices);
		setShowForm(false);
	};

	// Handle editing a service
	const handleEditService = (serviceData: ServiceData) => {
		const updatedServices = services.map((service) =>
			service.id === editingService?.id ? { ...serviceData, id: service.id } : service
		);
		setServices(updatedServices);
		onServicesChange(updatedServices);
		setEditingService(null);
	};

	// Handle deleting a service
	const handleDeleteService = (serviceId: string) => {
		const updatedServices = services.filter((service) => service.id !== serviceId);
		setServices(updatedServices);
		onServicesChange(updatedServices);
	};

	// Get category display name
	const getCategoryName = (category: ServiceCategory) => {
		return t(`categories.${category}`);
	};

	// Get category color
	const getCategoryColor = (category: ServiceCategory) => {
		const colors: Record<ServiceCategory, string> = {
			general_checkup: "bg-blue-100 text-blue-800",
			vaccination: "bg-green-100 text-green-800",
			surgery: "bg-red-100 text-red-800",
			dental_care: "bg-purple-100 text-purple-800",
			grooming: "bg-pink-100 text-pink-800",
			emergency: "bg-orange-100 text-orange-800",
			diagnostic: "bg-cyan-100 text-cyan-800",
			behavioral: "bg-indigo-100 text-indigo-800",
			nutrition: "bg-yellow-100 text-yellow-800",
			other: "bg-gray-100 text-gray-800",
		};
		return colors[category];
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h3 className="text-lg font-semibold">{t("setupTitle")}</h3>
					<p className="text-sm text-muted-foreground">
						{t("setupDescription")}
					</p>
				</div>
				<Button
					onClick={() => setShowForm(true)}
					disabled={showForm || editingService !== null}
					className="flex items-center gap-2"
				>
					<Plus className="h-4 w-4" />
					{t("addService")}
				</Button>
			</div>

			{/* Service Form */}
			{(showForm || editingService) && (
				<Card>
					<CardHeader>
						<CardTitle>
							{editingService ? t("editService") : t("addNewService")}
						</CardTitle>
					</CardHeader>
					<CardContent>
						<ServiceForm
							initialData={editingService || undefined}
							onSubmit={editingService ? handleEditService : handleAddService}
							onCancel={() => {
								setShowForm(false);
								setEditingService(null);
							}}
						/>
					</CardContent>
				</Card>
			)}

			{/* Services List */}
			{services.length > 0 && (
				<div className="space-y-4">
					<h4 className="font-medium">{t("addedServices")} ({services.length})</h4>
					<div className="grid gap-4">
						{services.map((service) => (
							<Card key={service.id} className="relative">
								<CardContent className="pt-6">
									<div className="flex items-start justify-between">
										<div className="flex-1 space-y-2">
											<div className="flex items-center gap-2">
												<h5 className="font-medium">{service.name}</h5>
												<Badge className={getCategoryColor(service.category)}>
													{getCategoryName(service.category)}
												</Badge>
											</div>
											
											{service.description && (
												<p className="text-sm text-muted-foreground">
													{service.description}
												</p>
											)}
											
											<div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
												{service.price && (
													<span>{t("price")}: {service.price} DZD</span>
												)}
												{service.duration && (
													<span>{t("duration")}: {service.duration} {t("minutes")}</span>
												)}
											</div>
											
											{service.requirements && service.requirements.length > 0 && (
												<div className="space-y-1">
													<p className="text-sm font-medium">{t("requirements")}:</p>
													<ul className="text-sm text-muted-foreground list-disc list-inside">
														{service.requirements.map((req, index) => (
															<li key={index}>{req}</li>
														))}
													</ul>
												</div>
											)}
										</div>
										
										<div className="flex items-center gap-2 ml-4">
											<Button
												variant="ghost"
												size="sm"
												onClick={() => setEditingService(service)}
												disabled={showForm || editingService !== null}
											>
												<Edit3 className="h-4 w-4" />
											</Button>
											<Button
												variant="ghost"
												size="sm"
												onClick={() => handleDeleteService(service.id!)}
												className="text-destructive hover:text-destructive"
											>
												<Trash2 className="h-4 w-4" />
											</Button>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			)}

			{/* Empty State */}
			{services.length === 0 && !showForm && (
				<Card className="border-dashed">
					<CardContent className="flex flex-col items-center justify-center py-12 text-center">
						<div className="rounded-full bg-muted p-3 mb-4">
							<Plus className="h-6 w-6 text-muted-foreground" />
						</div>
						<h4 className="font-medium mb-2">{t("noServices")}</h4>
						<p className="text-sm text-muted-foreground mb-4">
							{t("noServicesDescription")}
						</p>
						<Button onClick={() => setShowForm(true)}>
							{t("addFirstService")}
						</Button>
					</CardContent>
				</Card>
			)}

			{/* Info Note */}
			<div className="rounded-lg bg-blue-50 p-4">
				<p className="text-sm text-blue-800">
					{t("setupNote")}
				</p>
			</div>
		</div>
	);
}
